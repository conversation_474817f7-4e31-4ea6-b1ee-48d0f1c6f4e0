<?xml version='1.0' encoding='utf-8'?>
<graphml xmlns="http://graphml.graphdrawing.org/xmlns" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://graphml.graphdrawing.org/xmlns http://graphml.graphdrawing.org/xmlns/1.0/graphml.xsd">
  <key id="d9" for="edge" attr.name="file_path" attr.type="string" />
  <key id="d8" for="edge" attr.name="source_id" attr.type="string" />
  <key id="d7" for="edge" attr.name="keywords" attr.type="string" />
  <key id="d6" for="edge" attr.name="description" attr.type="string" />
  <key id="d5" for="edge" attr.name="weight" attr.type="double" />
  <key id="d4" for="node" attr.name="file_path" attr.type="string" />
  <key id="d3" for="node" attr.name="source_id" attr.type="string" />
  <key id="d2" for="node" attr.name="description" attr.type="string" />
  <key id="d1" for="node" attr.name="entity_type" attr.type="string" />
  <key id="d0" for="node" attr.name="entity_id" attr.type="string" />
  <graph edgedefault="undirected">
    <node id="PydanticAI">
      <data key="d0">PydanticAI</data>
      <data key="d1">organization</data>
      <data key="d2">PydanticAI is a Python agent framework aimed at simplifying the development of production-grade applications utilizing Generative AI.&lt;SEP&gt;PydanticAI is a Python agent framework designed to streamline the development of production-grade applications leveraging Generative AI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Agents">
      <data key="d0">Agents</data>
      <data key="d1">category</data>
      <data key="d2">Agents are a concept within the PydanticAI framework that facilitate interactions between different components and processes.&lt;SEP&gt;Agents are modular components within PydanticAI that facilitate automation and task execution in Generative AI applications.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Common Tools">
      <data key="d0">Common Tools</data>
      <data key="d1">category</data>
      <data key="d2">Common Tools in PydanticAI encompass a range of utilities designed to aid developers in application building and integration.&lt;SEP&gt;Common Tools refers to the set of utilities provided by PydanticAI to enhance the development process.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Dependencies">
      <data key="d0">Dependencies</data>
      <data key="d1">category</data>
      <data key="d2">Dependencies in PydanticAI represent the necessary packages and modules required to build applications effectively.&lt;SEP&gt;Dependencies refer to the necessary libraries and packages that PydanticAI applications may require for functionality.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Messages and chat history">
      <data key="d0">Messages and chat history</data>
      <data key="d1">category</data>
      <data key="d2">Messages and chat history pertain to the communication framework allowing information exchange within PydanticAI applications.&lt;SEP&gt;Messages and chat history represent the communication framework enabling interactions within PydanticAI applications.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Multi-agent Applications">
      <data key="d0">Multi-agent Applications</data>
      <data key="d1">category</data>
      <data key="d2">Multi-agent Applications describe the framework's capability to support collaborative operations among multiple agents.&lt;SEP&gt;Multi-agent Applications refers to the capability in PydanticAI for managing several agents working collaboratively.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Function Tools">
      <data key="d0">Function Tools</data>
      <data key="d1">category</data>
      <data key="d2">Function Tools include various functions provided by PydanticAI for enhancing the performance of Generative AI applications.&lt;SEP&gt;Function Tools offer predefined functionalities that enhance the capabilities of Generative AI applications built on PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Models">
      <data key="d0">Models</data>
      <data key="d1">category</data>
      <data key="d2">Models in PydanticAI are frameworks through which different AI models are implemented and operated.&lt;SEP&gt;Models in PydanticAI represent different AI frameworks and architectures that can be utilized within applications.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Anthropic">
      <data key="d0">Anthropic</data>
      <data key="d1">organization</data>
      <data key="d2">Anthropic is a model provider incorporated within PydanticAI, focusing on advanced AI functionalities and ethical AI development.&lt;SEP&gt;Anthropic is a model provider under the PydanticAI framework, offering advanced AI capabilities.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Bedrock">
      <data key="d0">Bedrock</data>
      <data key="d1">organization</data>
      <data key="d2">Bedrock is another model provider in the PydanticAI ecosystem, contributing unique AI functionalities.&lt;SEP&gt;Bedrock is another model provider that integrates various AI capabilities into the PydanticAI framework.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Cohere">
      <data key="d0">Cohere</data>
      <data key="d1">organization</data>
      <data key="d2">Cohere provides model options within PydanticAI which focus on natural language processing.&lt;SEP&gt;Cohere specializes in natural language processing and offers models that enhance PydanticAI's text analysis capabilities.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Gemini">
      <data key="d0">Gemini</data>
      <data key="d1">organization</data>
      <data key="d2">Gemini is a model provider contributing state-of-the-art AI functionalities to applications developed in PydanticAI.&lt;SEP&gt;Gemini is a model provider that adds specific AI solutions within the PydanticAI framework.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Google">
      <data key="d0">Google</data>
      <data key="d1">organization</data>
      <data key="d2">Google's models are integrated into PydanticAI to facilitate diverse AI applications.&lt;SEP&gt;Google's models are integrated within PydanticAI, providing robust solutions for diverse AI applications.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Groq">
      <data key="d0">Groq</data>
      <data key="d1">organization</data>
      <data key="d2">Groq is a model provider that offers AI capabilities incorporated into PydanticAI.&lt;SEP&gt;Groq is a model provider within PydanticAI offering efficient AI processing capabilities.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Mistral">
      <data key="d0">Mistral</data>
      <data key="d1">organization</data>
      <data key="d2">Mistral includes models and tools that expand the functionality of PydanticAI applications.&lt;SEP&gt;Mistral provides model solutions that enhance the functionality of PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="OpenAI">
      <data key="d0">OpenAI</data>
      <data key="d1">organization</data>
      <data key="d2">OpenAI contributes leading-edge models that augment the AI capabilities of PydanticAI.&lt;SEP&gt;OpenAI is a well-known AI provider within the PydanticAI ecosystem, offering several advanced models.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Graphs">
      <data key="d0">Graphs</data>
      <data key="d1">category</data>
      <data key="d2">Graphs in PydanticAI refer to structures used to represent data relationships and interactions in models.&lt;SEP&gt;Graphs within PydanticAI represent data relationships and are used to visualize and manage information flows.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Evals">
      <data key="d0">Evals</data>
      <data key="d1">category</data>
      <data key="d2">Evals provide methodologies for assessing the performance and accuracy of AI models within PydanticAI.&lt;SEP&gt;Evals represent a set of tools within PydanticAI for evaluating the performance of applications and models.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="MCP">
      <data key="d0">MCP</data>
      <data key="d1">category</data>
      <data key="d2">Model Context Protocol (MCP) defines how models interact and communicate within the PydanticAI framework.&lt;SEP&gt;Model Context Protocol (MCP) offers frameworks for understanding and managing model contexts in PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Client">
      <data key="d0">Client</data>
      <data key="d1">category</data>
      <data key="d2">Client refers to the component in PydanticAI that interfaces with users and other systems for functionality.&lt;SEP&gt;The Client component serves as the interface between users and the applications built with PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="MCP Run Python">
      <data key="d0">MCP Run Python</data>
      <data key="d1">category</data>
      <data key="d2">MCP Run Python allows Python execution within the context of the Model Context Protocol in PydanticAI.&lt;SEP&gt;MCP Run Python enables execution of Python scripts within the Model Context Protocol framework of PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Server">
      <data key="d0">Server</data>
      <data key="d1">category</data>
      <data key="d2">Server refers to the backend component that hosts PydanticAI applications and handles requests.&lt;SEP&gt;The Server aspect of PydanticAI facilitates backend processes that support application functionality and user interactions.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Command Line Interface (CLI)">
      <data key="d0">Command Line Interface (CLI)</data>
      <data key="d1">category</data>
      <data key="d2">CLI offers command line tools that allow users to interact with PydanticAI applications directly.&lt;SEP&gt;CLI provides command line tools supporting user interaction and control over PydanticAI systems.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Debugging and Monitoring">
      <data key="d0">Debugging and Monitoring</data>
      <data key="d1">category</data>
      <data key="d2">Debugging and Monitoring tools are critical for ensuring the operational effectiveness and reliability of PydanticAI applications.&lt;SEP&gt;Debugging and Monitoring tools are essential for maintaining the operational health of PydanticAI applications.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Unit testing">
      <data key="d0">Unit testing</data>
      <data key="d1">category</data>
      <data key="d2">Unit testing in PydanticAI refers to testing frameworks that ensure code quality and performance.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Examples">
      <data key="d0">Examples</data>
      <data key="d1">category</data>
      <data key="d2">Examples illustrate the practical application of PydanticAI across various use cases and scenarios.&lt;SEP&gt;Examples serve as practical demonstrations of PydanticAI usage across various applications.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Bank support">
      <data key="d0">Bank support</data>
      <data key="d1">category</data>
      <data key="d2">Bank support examples demonstrate PydanticAI's application within the financial sector.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Chat App with FastAPI">
      <data key="d0">Chat App with FastAPI</data>
      <data key="d1">category</data>
      <data key="d2">Chat App with FastAPI is an example of a real-time application built using PydanticAI components.&lt;SEP&gt;Chat App with FastAPI is an example of a real-time messaging application built using PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Flight booking">
      <data key="d0">Flight booking</data>
      <data key="d1">category</data>
      <data key="d2">Flight booking serves as an example of how PydanticAI can be utilized in the travel industry.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Pydantic Model">
      <data key="d0">Pydantic Model</data>
      <data key="d1">category</data>
      <data key="d2">Pydantic Model provides a standardized way to define data schemas for applications developed on this framework.&lt;SEP&gt;Pydantic Model provides structure for defining data schemas within the PydanticAI framework.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Question Graph">
      <data key="d0">Question Graph</data>
      <data key="d1">category</data>
      <data key="d2">Question Graph is a practical example in PydanticAI that embodies the application of AI in responding to queries.&lt;SEP&gt;Question Graph is an example within PydanticAI showcasing its application in question-answering scenarios.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="RAG">
      <data key="d0">RAG</data>
      <data key="d1">category</data>
      <data key="d2">RAG or Retrieval-Augmented Generation is an advanced application within PydanticAI showcasing enhanced response generation methodologies.&lt;SEP&gt;RAG stands for Retrieval-Augmented Generation, an example application demonstrating PydanticAI's capabilities.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="SQL Generation">
      <data key="d0">SQL Generation</data>
      <data key="d1">category</data>
      <data key="d2">SQL Generation showcases how PydanticAI can aid in database interactions and query generation.&lt;SEP&gt;SQL Generation within PydanticAI aids in automatic database query formulation and execution.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Stream markdown">
      <data key="d0">Stream markdown</data>
      <data key="d1">category</data>
      <data key="d2">Stream markdown provides an example of utilizing PydanticAI for streaming data outputs.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Stream whales">
      <data key="d0">Stream whales</data>
      <data key="d1">category</data>
      <data key="d2">Stream whales is used as an example showing PydanticAI's application in environmental monitoring.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Weather agent">
      <data key="d0">Weather agent</data>
      <data key="d1">category</data>
      <data key="d2">Weather agent is an application example demonstrating PydanticAI's use in meteorological data handling.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Unit Testing">
      <data key="d0">Unit Testing</data>
      <data key="d1">category</data>
      <data key="d2">Unit Testing frameworks in PydanticAI are used to validate components and ensure code quality.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Bank Support">
      <data key="d0">Bank Support</data>
      <data key="d1">category</data>
      <data key="d2">Bank Support refers to applications within the financial sector showcasing the capabilities of PydanticAI.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Flight Booking">
      <data key="d0">Flight Booking</data>
      <data key="d1">category</data>
      <data key="d2">Flight Booking demonstrates PydanticAI's application in managing travel-related services and reservations.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Stream Markdown">
      <data key="d0">Stream Markdown</data>
      <data key="d1">category</data>
      <data key="d2">Stream Markdown exemplifies the capabilities of PydanticAI in processing and rendering markdown documents in real-time.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Stream Whales">
      <data key="d0">Stream Whales</data>
      <data key="d1">category</data>
      <data key="d2">Stream Whales showcases PydanticAI's use in environmental research and data analysis related to marine life.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Weather Agent">
      <data key="d0">Weather Agent</data>
      <data key="d1">category</data>
      <data key="d2">Weather Agent is an application within PydanticAI used for processing and analyzing meteorological data.</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <node id="Components">
      <data key="d0">Components</data>
      <data key="d3">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d2">The Client interacts with different Components of PydanticAI, providing a user interface for the framework.</data>
      <data key="d1">UNKNOWN</data>
      <data key="d4">pydantic_ai_docs.md</data>
    </node>
    <edge source="PydanticAI" target="Agents">
      <data key="d5">20.0</data>
      <data key="d6">PydanticAI consists of Agents that automate various tasks within the development framework, enhancing productivity.&lt;SEP&gt;PydanticAI includes the concept of Agents as a core component for building responsive applications.</data>
      <data key="d7">automation, framework utilization&lt;SEP&gt;core concepts, application development</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Common Tools">
      <data key="d5">18.0</data>
      <data key="d6">Common Tools in PydanticAI provide essential resources that support the application development process.&lt;SEP&gt;PydanticAI leverages Common Tools to facilitate various development tasks and improve usability.</data>
      <data key="d7">development utilities, enhanced functionality&lt;SEP&gt;tooling support, development efficiency</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Models">
      <data key="d5">20.0</data>
      <data key="d6">Models are integral to PydanticAI, allowing developers to implement AI solutions effectively.&lt;SEP&gt;PydanticAI’s framework allows for the integration and usage of various AI Models for diverse applications.</data>
      <data key="d7">AI models, implementation&lt;SEP&gt;model integration, AI capabilities</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Dependencies">
      <data key="d5">8.0</data>
      <data key="d6">Dependencies ensure that PydanticAI applications have the necessary libraries for optimal functionality.</data>
      <data key="d7">library support, framework dependencies</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Graphs">
      <data key="d5">9.0</data>
      <data key="d6">Graphs are used in PydanticAI to help visualize data structures and relationships effectively.</data>
      <data key="d7">data visualization, structural representation</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="MCP">
      <data key="d5">10.0</data>
      <data key="d6">MCP outlines the interaction between models within the PydanticAI framework, facilitating seamless communication.</data>
      <data key="d7">model interaction, framework operation</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Bank Support">
      <data key="d5">9.0</data>
      <data key="d6">Bank Support applications are built using PydanticAI, demonstrating its use in financial services.</data>
      <data key="d7">application domain, practical use</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Flight Booking">
      <data key="d5">8.0</data>
      <data key="d6">Flight Booking showcases practical application of PydanticAI in managing travel services and reservation systems.</data>
      <data key="d7">travel services, application demonstration</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Chat App with FastAPI">
      <data key="d5">9.0</data>
      <data key="d6">Chat App with FastAPI exemplifies real-time application development capabilities of PydanticAI.</data>
      <data key="d7">real-time application, development capabilities</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="PydanticAI" target="Weather Agent">
      <data key="d5">8.0</data>
      <data key="d6">Weather Agent illustrates the application of PydanticAI in meteorological data processing and analysis.</data>
      <data key="d7">data analysis, application domain</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Messages and chat history" target="Client">
      <data key="d5">9.0</data>
      <data key="d6">The Client interacts with Messages and chat history to provide a user-friendly experience within PydanticAI applications.</data>
      <data key="d7">user experience, communication framework</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Models" target="Anthropic">
      <data key="d5">18.0</data>
      <data key="d6">Anthropic is one of the Model Providers available within the PydanticAI framework.&lt;SEP&gt;Anthropic models are utilized within PydanticAI to provide intelligent AI functionalities.</data>
      <data key="d7">AI integration, model provider&lt;SEP&gt;model provider, integration</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Models" target="Bedrock">
      <data key="d5">18.0</data>
      <data key="d6">Bedrock contributes to the range of Models supported by PydanticAI for diverse usage.&lt;SEP&gt;Bedrock's offerings enhance the capabilities of PydanticAI by adding diverse AI functionalities.</data>
      <data key="d7">model enhancement, provider contributions&lt;SEP&gt;model provider, integration</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Models" target="Cohere">
      <data key="d5">16.0</data>
      <data key="d6">Cohere models contribute specifically to natural language processing features in PydanticAI applications.&lt;SEP&gt;Cohere's models add natural language processing capabilities within the PydanticAI framework.</data>
      <data key="d7">NLP features, model specificity&lt;SEP&gt;NLP provider, model enhancement</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Models" target="Google">
      <data key="d5">9.0</data>
      <data key="d6">Google provides advanced AI Models for integration into PydanticAI applications.</data>
      <data key="d7">model provider, technology integration</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Models" target="OpenAI">
      <data key="d5">18.0</data>
      <data key="d6">OpenAI models enhance the capabilities of applications built on the PydanticAI framework.&lt;SEP&gt;OpenAI's cutting-edge models augment the capabilities and performance of applications developed in PydanticAI.</data>
      <data key="d7">model provider, application enhancement&lt;SEP&gt;performance enhancement, model provider</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Graphs" target="Evals">
      <data key="d5">18.0</data>
      <data key="d6">Evals utilize Graphs to showcase performance metrics and data relationships in PydanticAI applications.&lt;SEP&gt;Graphs and Evals help represent the performance and data relationships within PydanticAI applications.</data>
      <data key="d7">performance analysis, data structures&lt;SEP&gt;performance evaluation, data analysis</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="MCP" target="Server">
      <data key="d5">16.0</data>
      <data key="d6">The Server aspect of PydanticAI interfaces directly with the Model Context Protocol for application management.&lt;SEP&gt;The Server in PydanticAI utilizes the MCP for managing context and session information in applications.</data>
      <data key="d7">backend integration, context management&lt;SEP&gt;backend management, model context</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
    <edge source="Client" target="Components">
      <data key="d5">8.0</data>
      <data key="d6">The Client interacts with different Components of PydanticAI, providing a user interface for the framework.</data>
      <data key="d7">user interface, component interaction</data>
      <data key="d8">chunk-3bd464f6270793964e56215210e2d32b</data>
      <data key="d9">pydantic_ai_docs.md</data>
    </edge>
  </graph>
</graphml>
