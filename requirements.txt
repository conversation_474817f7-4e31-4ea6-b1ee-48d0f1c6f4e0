aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
anthropic==0.49.0
anyio==4.9.0
anytree==2.12.1
argcomplete==3.6.1
ascii_colors==0.5.2
attrs==25.3.0
autograd==1.7.0
beartype==0.18.5
blinker==1.9.0
boto3==1.37.25
botocore==1.37.25
cachetools==5.5.2
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
cohere==5.14.0
colorama==0.4.6
configparser==7.2.0
contourpy==1.3.1
cycler==0.12.1
Deprecated==1.2.18
distro==1.9.0
eval_type_backport==0.2.2
fastavro==1.10.0
filelock==3.18.0
fonttools==4.56.0
frozenlist==1.5.0
fsspec==2025.3.2
future==1.0.0
# gensim>=4.3.0  # Requires compilation - skip for now
gitdb==4.0.12
GitPython==3.1.44
google-auth==2.38.0
graspologic-native==1.2.4
griffe==1.7.2
groq==0.21.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.1
hyppo==0.4.0
idna==3.10
importlib_metadata==8.6.1
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.8
lightrag-hku==1.3.0
llvmlite==0.44.0
logfire-api==3.12.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.1
mcp==1.6.0
mdurl==0.1.2
mistralai==1.6.0
multidict==6.3.1
nano-vectordb==*******
narwhals==1.33.0
networkx==3.4.2
numba==0.61.0
numpy>=1.26.0
openai==1.70.0
opentelemetry-api==1.31.1
packaging==24.2
pandas==2.2.3
patsy==1.0.1
pillow==11.1.0
pipmaster==0.5.4
# POT==0.9.5  # Requires compilation - skip for now
prompt_toolkit==3.0.50
propcache==0.3.1
protobuf==5.29.4
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.1
pydantic-ai==0.0.49
pydantic-ai-slim==0.0.49
pydantic-evals==0.0.49
pydantic-graph==0.0.49
pydantic-settings==2.8.1
pydantic_core==2.33.0
pydeck==0.9.1
Pygments==2.19.1
pynndescent==0.5.13
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rich==14.0.0
rpds-py==0.24.0
rsa==4.9
s3transfer==0.11.4
scikit-learn==1.6.1
scipy>=1.12.0
seaborn==0.13.2
setuptools==78.1.0
six==1.17.0
smart-open==7.1.0
smmap==5.0.2
sniffio==1.3.1
sse-starlette==2.2.1
starlette==0.46.1
statsmodels==0.14.4
streamlit==1.44.1
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
toml==0.10.2
tornado==6.4.2
tqdm==4.67.1
types-requests==2.32.0.20250328
typing-inspection==0.4.0
typing_extensions==4.13.0
tzdata==2025.2
umap-learn==0.5.7
urllib3==2.3.0
uvicorn==0.34.0
watchdog==6.0.0
wcwidth==0.2.13
wrapt==1.17.2
XlsxWriter==3.2.2
yarl==1.18.3
zipp==3.21.0
