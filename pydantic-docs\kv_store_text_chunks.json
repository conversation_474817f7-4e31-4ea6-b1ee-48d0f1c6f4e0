{"chunk-3bd464f6270793964e56215210e2d32b": {"tokens": 713, "content": "# PydanticAI\n\n> Agent Framework / shim to use Pydantic with LLMs\n\nPydanticAI is a Python agent framework designed to make it less painful to build production grade\napplications with Generative AI.\n\n## Concepts documentation\n\n- [Agents](https://ai.pydantic.dev/agents/index.md)\n- [Common Tools](https://ai.pydantic.dev/common-tools/index.md)\n- [Dependencies](https://ai.pydantic.dev/dependencies/index.md)\n- [Messages and chat history](https://ai.pydantic.dev/message-history/index.md)\n- [Multi-agent Applications](https://ai.pydantic.dev/multi-agent-applications/index.md)\n- [Function Tools](https://ai.pydantic.dev/tools/index.md)\n\n## Models\n\n- [Model Providers](https://ai.pydantic.dev/models/index.md)\n- [Anthropic](https://ai.pydantic.dev/models/anthropic/index.md)\n- [Bedrock](https://ai.pydantic.dev/models/bedrock/index.md)\n- [Cohere](https://ai.pydantic.dev/models/cohere/index.md)\n- [Gemini](https://ai.pydantic.dev/models/gemini/index.md)\n- [Google](https://ai.pydantic.dev/models/google/index.md)\n- [Groq](https://ai.pydantic.dev/models/groq/index.md)\n- [Mistral](https://ai.pydantic.dev/models/mistral/index.md)\n- [OpenAI](https://ai.pydantic.dev/models/openai/index.md)\n\n## Graphs\n\n- [Graphs](https://ai.pydantic.dev/graph/index.md)\n\n## Evals\n\n- [Evals](https://ai.pydantic.dev/evals/index.md)\n\n## MCP\n\n- [Model Context Protocol (MCP)](https://ai.pydantic.dev/mcp/index.md)\n- [Client](https://ai.pydantic.dev/mcp/client/index.md)\n- [MCP Run Python](https://ai.pydantic.dev/mcp/run-python/index.md)\n- [Server](https://ai.pydantic.dev/mcp/server/index.md)\n\n## Optional\n\n- [Command Line Interface (CLI)](https://ai.pydantic.dev/cli/index.md)\n- [Debugging and Monitoring](https://ai.pydantic.dev/logfire/index.md)\n- [Unit testing](https://ai.pydantic.dev/testing/index.md)\n- [Examples](https://ai.pydantic.dev/examples/index.md)\n- [Bank support](https://ai.pydantic.dev/examples/bank-support/index.md)\n- [Chat App with FastAPI](https://ai.pydantic.dev/examples/chat-app/index.md)\n- [Flight booking](https://ai.pydantic.dev/examples/flight-booking/index.md)\n- [Pydantic Model](https://ai.pydantic.dev/examples/pydantic-model/index.md)\n- [Question Graph](https://ai.pydantic.dev/examples/question-graph/index.md)\n- [RAG](https://ai.pydantic.dev/examples/rag/index.md)\n- [SQL Generation](https://ai.pydantic.dev/examples/sql-gen/index.md)\n- [Stream markdown](https://ai.pydantic.dev/examples/stream-markdown/index.md)\n- [Stream whales](https://ai.pydantic.dev/examples/stream-whales/index.md)\n- [Weather agent](https://ai.pydantic.dev/examples/weather-agent/index.md)", "chunk_order_index": 0, "full_doc_id": "doc-3bd464f6270793964e56215210e2d32b", "file_path": "unknown_source"}}