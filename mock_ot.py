"""
Mock module for 'ot' (Python Optimal Transport) to avoid import errors.
This is a temporary workaround for missing POT package.
"""

# Mock the functions that graspologic might be trying to import
def sinkhorn(*args, **kwargs):
    """Mock sinkhorn function"""
    raise NotImplementedError("POT package not available - this is a mock implementation")

def emd(*args, **kwargs):
    """Mock earth mover's distance function"""
    raise NotImplementedError("POT package not available - this is a mock implementation")

def wasserstein_1d(*args, **kwargs):
    """Mock 1D Wasserstein distance function"""
    raise NotImplementedError("POT package not available - this is a mock implementation")

# Add any other commonly used functions from POT
class MockOT:
    """Mock class for POT functionality"""
    
    def __getattr__(self, name):
        def mock_func(*args, **kwargs):
            raise NotImplementedError(f"POT package not available - {name} is a mock implementation")
        return mock_func

# Create a mock module-level object
import sys
sys.modules['ot'] = MockOT()
