"""
Mock module for 'ot' (Python Optimal Transport) to avoid import errors.
This is a temporary workaround for missing POT package.
Also patches NumPy compatibility issues with NetworkX.
"""

import sys
import numpy as np

# Mock the functions that graspologic might be trying to import
def sinkhorn(*args, **kwargs):
    """Mock sinkhorn function"""
    raise NotImplementedError("POT package not available - this is a mock implementation")

def emd(*args, **kwargs):
    """Mock earth mover's distance function"""
    raise NotImplementedError("POT package not available - this is a mock implementation")

def wasserstein_1d(*args, **kwargs):
    """Mock 1D Wasserstein distance function"""
    raise NotImplementedError("POT package not available - this is a mock implementation")

# Add any other commonly used functions from POT
class MockOT:
    """Mock class for POT functionality"""

    def __getattr__(self, name):
        def mock_func(*args, **kwargs):
            raise NotImplementedError(f"POT package not available - {name} is a mock implementation")
        return mock_func

# Create a mock module-level object
sys.modules['ot'] = MockOT()

# Patch NumPy compatibility issue with NetworkX
# Add the missing np.float_ attribute for backward compatibility
if not hasattr(np, 'float_'):
    np.float_ = np.float64

print("Mock OT module loaded and NumPy compatibility patched")
