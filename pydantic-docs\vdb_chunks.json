{"embedding_dim": 1536, "data": [{"__id__": "chunk-3bd464f6270793964e56215210e2d32b", "__created_at__": 1748559389.4073443, "content": "# PydanticAI\n\n> Agent Framework / shim to use Pydantic with LLMs\n\nPydanticAI is a Python agent framework designed to make it less painful to build production grade\napplications with Generative AI.\n\n## Concepts documentation\n\n- [Agents](https://ai.pydantic.dev/agents/index.md)\n- [Common Tools](https://ai.pydantic.dev/common-tools/index.md)\n- [Dependencies](https://ai.pydantic.dev/dependencies/index.md)\n- [Messages and chat history](https://ai.pydantic.dev/message-history/index.md)\n- [Multi-agent Applications](https://ai.pydantic.dev/multi-agent-applications/index.md)\n- [Function Tools](https://ai.pydantic.dev/tools/index.md)\n\n## Models\n\n- [Model Providers](https://ai.pydantic.dev/models/index.md)\n- [Anthropic](https://ai.pydantic.dev/models/anthropic/index.md)\n- [Bedrock](https://ai.pydantic.dev/models/bedrock/index.md)\n- [Cohere](https://ai.pydantic.dev/models/cohere/index.md)\n- [Gemini](https://ai.pydantic.dev/models/gemini/index.md)\n- [Google](https://ai.pydantic.dev/models/google/index.md)\n- [Groq](https://ai.pydantic.dev/models/groq/index.md)\n- [Mistral](https://ai.pydantic.dev/models/mistral/index.md)\n- [OpenAI](https://ai.pydantic.dev/models/openai/index.md)\n\n## Graphs\n\n- [Graphs](https://ai.pydantic.dev/graph/index.md)\n\n## Evals\n\n- [Evals](https://ai.pydantic.dev/evals/index.md)\n\n## MCP\n\n- [Model Context Protocol (MCP)](https://ai.pydantic.dev/mcp/index.md)\n- [Client](https://ai.pydantic.dev/mcp/client/index.md)\n- [MCP Run Python](https://ai.pydantic.dev/mcp/run-python/index.md)\n- [Server](https://ai.pydantic.dev/mcp/server/index.md)\n\n## Optional\n\n- [Command Line Interface (CLI)](https://ai.pydantic.dev/cli/index.md)\n- [Debugging and Monitoring](https://ai.pydantic.dev/logfire/index.md)\n- [Unit testing](https://ai.pydantic.dev/testing/index.md)\n- [Examples](https://ai.pydantic.dev/examples/index.md)\n- [Bank support](https://ai.pydantic.dev/examples/bank-support/index.md)\n- [Chat App with FastAPI](https://ai.pydantic.dev/examples/chat-app/index.md)\n- [Flight booking](https://ai.pydantic.dev/examples/flight-booking/index.md)\n- [Pydantic Model](https://ai.pydantic.dev/examples/pydantic-model/index.md)\n- [Question Graph](https://ai.pydantic.dev/examples/question-graph/index.md)\n- [RAG](https://ai.pydantic.dev/examples/rag/index.md)\n- [SQL Generation](https://ai.pydantic.dev/examples/sql-gen/index.md)\n- [Stream markdown](https://ai.pydantic.dev/examples/stream-markdown/index.md)\n- [Stream whales](https://ai.pydantic.dev/examples/stream-whales/index.md)\n- [Weather agent](https://ai.pydantic.dev/examples/weather-agent/index.md)", "full_doc_id": "doc-3bd464f6270793964e56215210e2d32b", "file_path": "unknown_source"}], "matrix": "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"}