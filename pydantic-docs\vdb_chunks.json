{"embedding_dim": 1536, "data": [{"__id__": "chunk-3bd464f6270793964e56215210e2d32b", "__created_at__": 1748557556.0085573, "content": "# PydanticAI\n\n> Agent Framework / shim to use Pydantic with LLMs\n\nPydanticAI is a Python agent framework designed to make it less painful to build production grade\napplications with Generative AI.\n\n## Concepts documentation\n\n- [Agents](https://ai.pydantic.dev/agents/index.md)\n- [Common Tools](https://ai.pydantic.dev/common-tools/index.md)\n- [Dependencies](https://ai.pydantic.dev/dependencies/index.md)\n- [Messages and chat history](https://ai.pydantic.dev/message-history/index.md)\n- [Multi-agent Applications](https://ai.pydantic.dev/multi-agent-applications/index.md)\n- [Function Tools](https://ai.pydantic.dev/tools/index.md)\n\n## Models\n\n- [Model Providers](https://ai.pydantic.dev/models/index.md)\n- [Anthropic](https://ai.pydantic.dev/models/anthropic/index.md)\n- [Bedrock](https://ai.pydantic.dev/models/bedrock/index.md)\n- [Cohere](https://ai.pydantic.dev/models/cohere/index.md)\n- [Gemini](https://ai.pydantic.dev/models/gemini/index.md)\n- [Google](https://ai.pydantic.dev/models/google/index.md)\n- [Groq](https://ai.pydantic.dev/models/groq/index.md)\n- [Mistral](https://ai.pydantic.dev/models/mistral/index.md)\n- [OpenAI](https://ai.pydantic.dev/models/openai/index.md)\n\n## Graphs\n\n- [Graphs](https://ai.pydantic.dev/graph/index.md)\n\n## Evals\n\n- [Evals](https://ai.pydantic.dev/evals/index.md)\n\n## MCP\n\n- [Model Context Protocol (MCP)](https://ai.pydantic.dev/mcp/index.md)\n- [Client](https://ai.pydantic.dev/mcp/client/index.md)\n- [MCP Run Python](https://ai.pydantic.dev/mcp/run-python/index.md)\n- [Server](https://ai.pydantic.dev/mcp/server/index.md)\n\n## Optional\n\n- [Command Line Interface (CLI)](https://ai.pydantic.dev/cli/index.md)\n- [Debugging and Monitoring](https://ai.pydantic.dev/logfire/index.md)\n- [Unit testing](https://ai.pydantic.dev/testing/index.md)\n- [Examples](https://ai.pydantic.dev/examples/index.md)\n- [Bank support](https://ai.pydantic.dev/examples/bank-support/index.md)\n- [Chat App with FastAPI](https://ai.pydantic.dev/examples/chat-app/index.md)\n- [Flight booking](https://ai.pydantic.dev/examples/flight-booking/index.md)\n- [Pydantic Model](https://ai.pydantic.dev/examples/pydantic-model/index.md)\n- [Question Graph](https://ai.pydantic.dev/examples/question-graph/index.md)\n- [RAG](https://ai.pydantic.dev/examples/rag/index.md)\n- [SQL Generation](https://ai.pydantic.dev/examples/sql-gen/index.md)\n- [Stream markdown](https://ai.pydantic.dev/examples/stream-markdown/index.md)\n- [Stream whales](https://ai.pydantic.dev/examples/stream-whales/index.md)\n- [Weather agent](https://ai.pydantic.dev/examples/weather-agent/index.md)", "full_doc_id": "doc-3bd464f6270793964e56215210e2d32b", "file_path": "unknown_source"}], "matrix": "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"}