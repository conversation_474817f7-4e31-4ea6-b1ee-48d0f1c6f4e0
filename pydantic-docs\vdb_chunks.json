{"embedding_dim": 1536, "data": [{"__id__": "chunk-3bd464f6270793964e56215210e2d32b", "__created_at__": 1748559864.406833, "content": "# PydanticAI\n\n> Agent Framework / shim to use Pydantic with LLMs\n\nPydanticAI is a Python agent framework designed to make it less painful to build production grade\napplications with Generative AI.\n\n## Concepts documentation\n\n- [Agents](https://ai.pydantic.dev/agents/index.md)\n- [Common Tools](https://ai.pydantic.dev/common-tools/index.md)\n- [Dependencies](https://ai.pydantic.dev/dependencies/index.md)\n- [Messages and chat history](https://ai.pydantic.dev/message-history/index.md)\n- [Multi-agent Applications](https://ai.pydantic.dev/multi-agent-applications/index.md)\n- [Function Tools](https://ai.pydantic.dev/tools/index.md)\n\n## Models\n\n- [Model Providers](https://ai.pydantic.dev/models/index.md)\n- [Anthropic](https://ai.pydantic.dev/models/anthropic/index.md)\n- [Bedrock](https://ai.pydantic.dev/models/bedrock/index.md)\n- [Cohere](https://ai.pydantic.dev/models/cohere/index.md)\n- [Gemini](https://ai.pydantic.dev/models/gemini/index.md)\n- [Google](https://ai.pydantic.dev/models/google/index.md)\n- [Groq](https://ai.pydantic.dev/models/groq/index.md)\n- [Mistral](https://ai.pydantic.dev/models/mistral/index.md)\n- [OpenAI](https://ai.pydantic.dev/models/openai/index.md)\n\n## Graphs\n\n- [Graphs](https://ai.pydantic.dev/graph/index.md)\n\n## Evals\n\n- [Evals](https://ai.pydantic.dev/evals/index.md)\n\n## MCP\n\n- [Model Context Protocol (MCP)](https://ai.pydantic.dev/mcp/index.md)\n- [Client](https://ai.pydantic.dev/mcp/client/index.md)\n- [MCP Run Python](https://ai.pydantic.dev/mcp/run-python/index.md)\n- [Server](https://ai.pydantic.dev/mcp/server/index.md)\n\n## Optional\n\n- [Command Line Interface (CLI)](https://ai.pydantic.dev/cli/index.md)\n- [Debugging and Monitoring](https://ai.pydantic.dev/logfire/index.md)\n- [Unit testing](https://ai.pydantic.dev/testing/index.md)\n- [Examples](https://ai.pydantic.dev/examples/index.md)\n- [Bank support](https://ai.pydantic.dev/examples/bank-support/index.md)\n- [Chat App with FastAPI](https://ai.pydantic.dev/examples/chat-app/index.md)\n- [Flight booking](https://ai.pydantic.dev/examples/flight-booking/index.md)\n- [Pydantic Model](https://ai.pydantic.dev/examples/pydantic-model/index.md)\n- [Question Graph](https://ai.pydantic.dev/examples/question-graph/index.md)\n- [RAG](https://ai.pydantic.dev/examples/rag/index.md)\n- [SQL Generation](https://ai.pydantic.dev/examples/sql-gen/index.md)\n- [Stream markdown](https://ai.pydantic.dev/examples/stream-markdown/index.md)\n- [Stream whales](https://ai.pydantic.dev/examples/stream-whales/index.md)\n- [Weather agent](https://ai.pydantic.dev/examples/weather-agent/index.md)", "full_doc_id": "doc-3bd464f6270793964e56215210e2d32b", "file_path": "pydantic_ai_docs.md"}], "matrix": "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"}